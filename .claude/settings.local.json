{"permissions": {"allow": ["Bash(git add:*)", "Bash(git commit:*)", "Bash(find:*)", "Bash(grep:*)", "<PERSON><PERSON>(mvn test:*)", "<PERSON><PERSON>(mvn clean:*)", "<PERSON><PERSON>(mvn checkstyle:*)", "<PERSON><PERSON>(curl:*)", "Bash(lsof:*)", "Bash(kill:*)", "<PERSON><PERSON>(mvn:*)", "Bash(git checkout:*)", "Bash(git branch:*)", "Bash(git push:*)", "Bash(git reset:*)", "<PERSON><PERSON>(chmod:*)", "Bash(bash:*)", "Bash(./test-audit-events-api.sh:*)", "WebFetch(domain:spring.io)", "WebFetch(domain:github.com)", "WebFetch(domain:github.com)", "WebFetch(domain:github.com)", "WebFetch(domain:github.com)", "<PERSON><PERSON>(timeout:*)", "Bash(./test-rsa-decrypt-load.sh:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(gtimeout:*)", "<PERSON><PERSON>(k6 run:*)", "WebFetch(domain:commons.apache.org)", "Bash(git rm:*)", "Bash(./test-key-rotation.sh:*)", "Bash(ls:*)", "Bash(./test-unified-api.sh:*)", "Bash(--header 'Content-Type: application/json' )", "Bash(--data '[]')"], "deny": []}}