package com.kerryprops.kip.crypto;

import com.kerryprops.kip.exception.DecryptException;
import com.kerryprops.kip.exception.EncryptException;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.DefaultResourceLoader;
import javax.crypto.Cipher;
import java.io.IOException;
import java.security.GeneralSecurityException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

/**
 * Unit tests for exception branches in RsaCryptoService encrypt/decrypt methods.
 */
@ExtendWith(MockitoExtension.class)
class RsaCryptoServiceTest {

    private final RsaCryptoService rsaCryptoService = createRsaCryptoService();

    @Mock
    private GenericObjectPool<Cipher> mockEncryptCipherPool;
    @Mock
    private GenericObjectPool<Cipher> mockDecryptCipherPool;
    @Mock
    private Cipher mockCipher;
    private RsaCryptoService rsaCryptoServiceWithMock;

    @BeforeEach
    void setUp() throws Exception {
        RsaProperties rsaProperties = new RsaProperties();
        rsaCryptoServiceWithMock = Mockito.spy(
            new RsaCryptoService(
                rsaProperties,
                new KeyPairManager(new DefaultResourceLoader(), rsaProperties)
            )
        );
    }

    @Test
    @DisplayName("测试rsa加密-正常输入-正常加密")
    void testEncrypt_validInput() throws Exception {
        // Arrange
        var plainText = "Hello, RSA!";
        // Act
        var encryptedText = rsaCryptoService.encrypt(plainText);
        // Assert
        assertFalse(encryptedText.isEmpty());
    }

    @Test
    @DisplayName("测试rsa加密-输入为null-抛出异常")
    void testEncrypt_nullInput() {
        // Arrange/Act/Assert
        assertThrows(EncryptException.class, () -> rsaCryptoService.encrypt(null));
    }

    @Test
    @DisplayName("测试rsa加密解密-相互工作验证")
    void testEncryptDecrypt_worksTogether() throws Exception {
        // Arrange
        var plainText = "Test Message Confidential";
        // Act
        var encryptedText = rsaCryptoService.encrypt(plainText);
        var decryptedText = rsaCryptoService.decrypt(encryptedText);
        // Assert
        assertEquals(plainText, decryptedText);
    }

    @Test
    @DisplayName("测试rsa解密-有效输入-解密成功")
    void testDecrypt_validInput() throws Exception {
        // Arrange
        var plainText = "Decryption Test";
        var encryptedText = rsaCryptoService.encrypt(plainText);
        // Act
        var decryptedText = rsaCryptoService.decrypt(encryptedText);
        // Assert
        assertEquals(plainText, decryptedText);
    }

    @Test
    @DisplayName("测试rsa解密-无效输入-抛出异常")
    void testDecrypt_invalidInput() {
        // Arrange
        var invalidEncryptedText = "InvalidBase64";
        // Act/Assert
        assertThrows(DecryptException.class, () -> rsaCryptoService.decrypt(invalidEncryptedText));
    }

    @Test
    @DisplayName("测试rsa解密-输入为null-抛出异常")
    void testDecrypt_nullInput() {
        // Act/Assert
        assertThrows(DecryptException.class, () -> rsaCryptoService.decrypt(null));
    }

    /**
     * Should throw EncryptException when encryption pool borrowObject() throws IOException.
     */
    @Test
    @DisplayName("encrypt throws EncryptException when borrowObject throws IOException")
    void testEncryptThrowsExceptionWhenBorrowObjectFails() throws Exception {
        // Arrange
        var encryptField = RsaCryptoService.class.getDeclaredField("encryptCipherPool");
        encryptField.setAccessible(true);
        encryptField.set(rsaCryptoServiceWithMock, mockEncryptCipherPool);
        when(mockEncryptCipherPool.borrowObject()).thenThrow(new IOException("pool error"));
        // Act & Assert
        assertThrows(EncryptException.class, () -> rsaCryptoServiceWithMock.encrypt("test"));
    }

    /**
     * Should throw EncryptException when encryption doFinal throws IllegalStateException.
     */
    @Test
    @DisplayName("encrypt throws EncryptException when doFinal throws IllegalStateException")
    void testEncryptThrowsExceptionWhenDoFinalFails() throws Exception {
        // Arrange
        var encryptField = RsaCryptoService.class.getDeclaredField("encryptCipherPool");
        encryptField.setAccessible(true);
        encryptField.set(rsaCryptoServiceWithMock, mockEncryptCipherPool);
        when(mockEncryptCipherPool.borrowObject()).thenReturn(mockCipher);
        doThrow(new IllegalStateException("encrypt error")).when(mockCipher).doFinal(any());
        // Act & Assert
        assertThrows(EncryptException.class, () -> rsaCryptoServiceWithMock.encrypt("test"));
    }

    /**
     * Should throw DecryptException when decryption pool borrowObject() throws IOException.
     */
    @Test
    @DisplayName("decrypt throws DecryptException when borrowObject throws IOException")
    void testDecryptThrowsExceptionWhenBorrowObjectFails() throws Exception {
        // Arrange
        var decryptField = RsaCryptoService.class.getDeclaredField("decryptCipherPool");
        decryptField.setAccessible(true);
        decryptField.set(rsaCryptoServiceWithMock, mockDecryptCipherPool);
        when(mockDecryptCipherPool.borrowObject()).thenThrow(new IOException("pool error"));
        // Act & Assert
        assertThrows(DecryptException.class, () -> rsaCryptoServiceWithMock.decrypt("dGVzdA==")); // "test" base64
    }

    /**
     * Should throw DecryptException when decryption doFinal throws IllegalStateException.
     */
    @Test
    @DisplayName("decrypt throws DecryptException when doFinal throws IllegalStateException")
    void testDecryptThrowsExceptionWhenDoFinalFails() throws Exception {
        // Arrange
        var decryptField = RsaCryptoService.class.getDeclaredField("decryptCipherPool");
        decryptField.setAccessible(true);
        decryptField.set(rsaCryptoServiceWithMock, mockDecryptCipherPool);
        when(mockDecryptCipherPool.borrowObject()).thenReturn(mockCipher);
        doThrow(new IllegalStateException("decrypt error")).when(mockCipher).doFinal(any());
        // Act & Assert
        assertThrows(DecryptException.class, () -> rsaCryptoServiceWithMock.decrypt("dGVzdA==")); // "test" base64
    }

    private static RsaCryptoService createRsaCryptoService() {
        try {
            RsaProperties rsaProperties = new RsaProperties();
            return new RsaCryptoService(rsaProperties, new KeyPairManager(new DefaultResourceLoader(), rsaProperties));
        } catch (GeneralSecurityException | IOException e) {
            throw new RuntimeException(e);
        }
    }

}