package com.kerryprops.kip.integration;

import com.kerryprops.kip.crypto.RsaCryptoController;
import com.kerryprops.kip.crypto.RsaCryptoService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;

import static io.restassured.RestAssured.given;
import static org.assertj.core.api.Assertions.assertThat;

/**
 * RsaCryptoControllerIntegrationTest.
 *
 * <AUTHOR> Yu
 **/
class RsaCryptoControllerIntegrationTest extends BaseIntegrationTest {

    @Resource
    private RsaCryptoService rsaService;

    @Test
    @DisplayName("getRsaPublicKey-正常请求-返回公钥")
    void getRsaPublicKey_validRequest_returnPublicKeySuccessfully() {
        // Act & Assert
        RsaCryptoController.PublicKeyResponse response = given().when()
                                                                .get("/crypto/rsa/public_key")
                                                                .then()
                                                                .statusCode(HttpStatus.OK.value())
                                                                .extract()
                                                                .as(RsaCryptoController.PublicKeyResponse.class);

        assertThat(response).isNotNull();
        assertThat(response.base64PublicKey()).isEqualTo(rsaService.getRsaPublicKey());
    }

    @Test
    @DisplayName("encryptRsa-正常请求-返回加密数据")
    void encryptRsa_validRequest_returnEncryptedDataSuccessfully() {
        RsaCryptoController.EncryptRequest request = new RsaCryptoController.EncryptRequest("testPlainText");

        given().body(request)
               .when()
               .post("/crypto/rsa/encrypt")
               .then()
               .statusCode(HttpStatus.OK.value())
               .extract()
               .as(RsaCryptoController.EncryptResponse.class);
    }

    @Test
    @DisplayName("encryptRsa-无效请求参数-返回500")
    void encryptRsa_invalidRequest_returnBadRequest() {
        // Arrange
        RsaCryptoController.EncryptRequest invalidRequest = new RsaCryptoController.EncryptRequest(null);

        // Act & Assert
        given().body(invalidRequest)
               .when()
               .post("/crypto/rsa/encrypt")
               .then()
               .statusCode(HttpStatus.INTERNAL_SERVER_ERROR.value());
    }

    @Test
    @DisplayName("decryptRsa-正常请求-返回解密数据")
    void decryptRsa_validRequest_returnDecryptedDataSuccessfully() {
        // Arrange
        String encryptedData = rsaService.encrypt("testPlainText");
        RsaCryptoController.DecryptRequest request = new RsaCryptoController.DecryptRequest(encryptedData);

        // Act & Assert
        given().body(request)
               .when()
               .post("/crypto/rsa/decrypt")
               .then()
               .statusCode(HttpStatus.OK.value())
               .extract()
               .as(RsaCryptoController.DecryptResponse.class);
    }

}