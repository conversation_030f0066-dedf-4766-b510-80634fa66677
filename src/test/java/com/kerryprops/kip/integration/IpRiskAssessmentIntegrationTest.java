package com.kerryprops.kip.integration;

import com.kerryprops.kip.ApplicationLauncher;
import com.kerryprops.kip.riskcontrol.constant.RiskResult;
import com.kerryprops.kip.riskcontrol.dto.IpRiskAssessRequest;
import com.kerryprops.kip.riskcontrol.dto.IpRiskAssessResponse;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Integration tests for IP risk assessment functionality.
 * Tests the complete flow from HTTP request to Drools rules evaluation.
 */
@SpringBootTest(classes = ApplicationLauncher.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
class IpRiskAssessmentIntegrationTest {

    @LocalServerPort
    private int port;

    @Autowired
    private TestRestTemplate restTemplate;

    @Test
    @DisplayName("Should assess IP risk and return appropriate response for China IP")
    void shouldAssessIpRiskForChinaIp() {
        // Given
        String url = "http://localhost:" + port + "/risk/ip-assess";
        IpRiskAssessRequest request = new IpRiskAssessRequest("************"); // Google China IP

        // When
        ResponseEntity<IpRiskAssessResponse> response = restTemplate.postForEntity(url, request, IpRiskAssessResponse.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        
        IpRiskAssessResponse body = response.getBody();
        assertThat(body.getIpAddress()).isEqualTo("************");
        assertThat(body.getRiskResult()).isIn(RiskResult.PASS, RiskResult.REJECT); // Depends on whether MaxMind DB is available
        assertThat(body.getAssessmentTime()).isNotNull();
        assertThat(body.getAssessmentDetails()).isNotBlank();
    }

    @Test
    @DisplayName("Should assess IP risk and return REJECT for US IP")
    void shouldAssessIpRiskForUsIp() {
        // Given
        String url = "http://localhost:" + port + "/risk/ip-assess";
        IpRiskAssessRequest request = new IpRiskAssessRequest("*******"); // Google DNS

        // When
        ResponseEntity<IpRiskAssessResponse> response = restTemplate.postForEntity(url, request, IpRiskAssessResponse.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        
        IpRiskAssessResponse body = response.getBody();
        assertThat(body.getIpAddress()).isEqualTo("*******");
        assertThat(body.getRiskResult()).isEqualTo(RiskResult.REJECT); // Should always be REJECT for non-China IPs
        assertThat(body.getAssessmentTime()).isNotNull();
        assertThat(body.getAssessmentDetails()).isNotBlank();
    }

    @Test
    @DisplayName("Should return bad request for invalid IP address")
    void shouldReturnBadRequestForInvalidIp() {
        // Given
        String url = "http://localhost:" + port + "/risk/ip-assess";
        IpRiskAssessRequest request = new IpRiskAssessRequest("invalid.ip.address");

        // When
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
    }

    @Test
    @DisplayName("Should handle private IP addresses gracefully")
    void shouldHandlePrivateIpAddresses() {
        // Given
        String url = "http://localhost:" + port + "/risk/ip-assess";
        IpRiskAssessRequest request = new IpRiskAssessRequest("***********");

        // When
        ResponseEntity<IpRiskAssessResponse> response = restTemplate.postForEntity(url, request, IpRiskAssessResponse.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        
        IpRiskAssessResponse body = response.getBody();
        assertThat(body.getIpAddress()).isEqualTo("***********");
        assertThat(body.getRiskResult()).isEqualTo(RiskResult.REJECT); // Private IPs should be rejected
        assertThat(body.getAssessmentTime()).isNotNull();
    }

    @Test
    @DisplayName("Should handle IPv6 addresses")
    void shouldHandleIpv6Addresses() {
        // Given
        String url = "http://localhost:" + port + "/risk/ip-assess";
        IpRiskAssessRequest request = new IpRiskAssessRequest("2001:4860:4860::8888"); // Google IPv6 DNS

        // When
        ResponseEntity<IpRiskAssessResponse> response = restTemplate.postForEntity(url, request, IpRiskAssessResponse.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        
        IpRiskAssessResponse body = response.getBody();
        assertThat(body.getIpAddress()).isEqualTo("2001:4860:4860::8888");
        assertThat(body.getRiskResult()).isEqualTo(RiskResult.REJECT); // Should be REJECT for non-China IPs
        assertThat(body.getAssessmentTime()).isNotNull();
    }
}
