package com.kerryprops.kip.integration;

import com.kerryprops.kip.riskcontrol.constant.TxRiskSceneCode;
import com.kerryprops.kip.riskcontrol.dto.PhoneTxRiskAssessRequest;
import io.restassured.http.ContentType;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.equalTo;

/**
 * Integration tests for RiskAssessmentController endpoints using Rest Assured.
 */
@Disabled
class RiskAssessmentIntegrationTest extends BaseIntegrationTest {

    /**
     * Test low risk for valid normal phone number.
     */
    @Test
    @DisplayName("Should return low risk for normal phone number")
    void shouldReturnLowRiskForNormalPhoneNumber() {
        // Arrange
        var requestBody = Map.of("phoneNumbers", List.of("13800138000"));
        // Act
        given().contentType(ContentType.JSON)
               .body(requestBody)
               .when()
               .post("/risk/assess")
               .then()
               .body("[0]", org.hamcrest.Matchers.is(org.hamcrest.Matchers.notNullValue()))
               .body("[0].phoneNumber", equalTo("13800138000"))
               .body("[0].riskLevel", equalTo("low"))
               .body("[0].riskTypes.size()", equalTo(1))
               .body("[0].riskTypes[0]", equalTo("none"))
               .body("[0].lastUpdated", org.hamcrest.Matchers.is(org.hamcrest.Matchers.notNullValue()));
    }

    @Test
    @DisplayName("assessPhoneNumber-正常手机号-正常调用腾讯风控接口")
    void assessPhoneNumber_validPhoneNumber_ok() {
        PhoneTxRiskAssessRequest request = new PhoneTxRiskAssessRequest();
        request.setPhoneNumber("17887991578");
        request.setOpenId("oudWQ5W7D6XRq8Uy4irQFammYzqI");
        request.setIp("127.0.0.1");
        given().contentType(ContentType.JSON)
               .body(request)
               .when()
               .post("/risk/tencent-assess")
               .then()
               .statusCode(200);
        //  频繁用相同参数调用，会导致 PASS 变 REVIEW
        // .body("riskResult", equalTo("PASS"));
    }

    @ParameterizedTest
    @DisplayName("测试腾讯风控接口参数缺失情况")
    @MethodSource("phoneTxRiskAssessRequestParams")
    void assessPhoneNumber_withMissingParameters_shouldStillWork(PhoneTxRiskAssessRequest request, int responseStatus,
                                                                 String testDescription) {
        // Act & Assert
        given().contentType(ContentType.JSON)
               .body(request)
               .when()
               .post("/risk/tencent-assess")
               .then()
               .statusCode(responseStatus);
    }

    /**
     * Test validation error for empty phoneNumbers list (should trigger BadRequestException).
     */
    @Test
    @DisplayName("Should return 400 Bad Request for empty phoneNumbers list")
    void shouldReturnBadRequestForEmptyPhoneNumbers() {
        // Arrange
        var requestBody = Map.of("phoneNumbers", List.of());
        // Act & Assert
        given().contentType(ContentType.JSON)
               .body(requestBody)
               .when()
               .post("/risk/assess")
               .then()
               .statusCode(400);
    }

    /**
     * 提供测试数据：测试三个参数分别为空的情况.
     */
    private static Stream<Arguments> phoneTxRiskAssessRequestParams() {
        PhoneTxRiskAssessRequest request1 = new PhoneTxRiskAssessRequest();
        request1.setOpenId("oudWQ5W7D6XRq8Uy4irQFammYzqI");
        request1.setIp("127.0.0.1");
        request1.setSceneCode(TxRiskSceneCode.LOGIN);

        PhoneTxRiskAssessRequest request2 = new PhoneTxRiskAssessRequest();
        request2.setPhoneNumber("17887991578");
        request2.setIp("127.0.0.1");
        request2.setSceneCode(TxRiskSceneCode.LOGIN);

        PhoneTxRiskAssessRequest request3 = new PhoneTxRiskAssessRequest();
        request3.setPhoneNumber("17887991578");
        request3.setOpenId("oudWQ5W7D6XRq8Uy4irQFammYzqI");
        request3.setSceneCode(TxRiskSceneCode.LOGIN);

        PhoneTxRiskAssessRequest request4 = new PhoneTxRiskAssessRequest();
        request4.setPhoneNumber("17887991578");
        request4.setOpenId("oudWQ5W7D6XRq8Uy4irQFammYzqI");
        request4.setSceneCode(TxRiskSceneCode.LOGIN);

        PhoneTxRiskAssessRequest request5 = new PhoneTxRiskAssessRequest();
        request5.setIp("127.0.0.1");
        return Stream.of(Arguments.of(request1, 200, "手机号为空"), Arguments.of(request2, 200, "微信ID为空"),
                         Arguments.of(request3, 500, "OPEN_ID-IP为空"),
                         Arguments.of(request4, 500, "PHONE_NUMBER-IP为空"),
                         Arguments.of(request5, 500, "OPEN_ID-id和手机号同时为空"));
    }

}
