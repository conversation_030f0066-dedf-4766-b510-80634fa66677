package com.kerryprops.kip.riskcontrol.service;

import com.kerryprops.kip.riskcontrol.constant.RiskResult;
import com.kerryprops.kip.riskcontrol.dto.IpRiskAssessResponse;
import com.kerryprops.kip.riskcontrol.model.IpGeolocation;
import com.kerryprops.kip.riskcontrol.model.IpRiskFact;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit tests for IpRiskAssessmentService.
 * Tests the integration of IP geolocation and Drools rules engine for risk assessment.
 */
@ExtendWith(MockitoExtension.class)
class IpRiskAssessmentServiceTest {

    @Mock
    private IpGeolocationService ipGeolocationService;

    @Mock
    private DroolsRuleEngineService droolsRuleEngineService;

    private IpRiskAssessmentService ipRiskAssessmentService;

    @BeforeEach
    void setUp() {
        ipRiskAssessmentService = new IpRiskAssessmentService(ipGeolocationService, droolsRuleEngineService);
    }

    @Test
    @DisplayName("Should return PASS when IP is from mainland China")
    void shouldReturnPassWhenIpIsFromMainlandChina() {
        // Given
        String ipAddress = "************";
        IpGeolocation geolocation = IpGeolocation.createValid(ipAddress, "China", "CN")
            .withSubdivision("Beijing")
            .withCity("Beijing")
            .withCoordinates(39.9042, 116.4074);
        
        IpRiskFact riskFactWithResult = IpRiskFact.create(ipAddress, geolocation);
        riskFactWithResult.setRiskResult(RiskResult.PASS);
        riskFactWithResult.setAssessmentDetails("IP address geolocated to mainland China");

        when(ipGeolocationService.isServiceAvailable()).thenReturn(true);
        when(ipGeolocationService.geolocateIp(ipAddress)).thenReturn(geolocation);
        when(droolsRuleEngineService.isRuleEngineAvailable()).thenReturn(true);
        when(droolsRuleEngineService.evaluateIpRisk(any(IpRiskFact.class))).thenReturn(riskFactWithResult);

        // When
        IpRiskAssessResponse response = ipRiskAssessmentService.assessIpRisk(ipAddress);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getIpAddress()).isEqualTo(ipAddress);
        assertThat(response.getRiskResult()).isEqualTo(RiskResult.PASS);
        assertThat(response.getCountry()).isEqualTo("China");
        assertThat(response.getCountryIsoCode()).isEqualTo("CN");
        assertThat(response.getAssessmentDetails()).contains("mainland China");
        assertThat(response.getAssessmentTime()).isNotNull();

        verify(ipGeolocationService).geolocateIp(ipAddress);
        verify(droolsRuleEngineService).evaluateIpRisk(any(IpRiskFact.class));
    }

    @Test
    @DisplayName("Should return REJECT when IP is from outside mainland China")
    void shouldReturnRejectWhenIpIsFromOutsideMainlandChina() {
        // Given
        String ipAddress = "*******";
        IpGeolocation geolocation = IpGeolocation.createValid(ipAddress, "United States", "US")
            .withSubdivision("California")
            .withCity("Mountain View")
            .withCoordinates(37.4056, -122.0775);
        
        IpRiskFact riskFactWithResult = IpRiskFact.create(ipAddress, geolocation);
        riskFactWithResult.setRiskResult(RiskResult.REJECT);
        riskFactWithResult.setAssessmentDetails("IP address geolocated to United States, outside mainland China");

        when(ipGeolocationService.isServiceAvailable()).thenReturn(true);
        when(ipGeolocationService.geolocateIp(ipAddress)).thenReturn(geolocation);
        when(droolsRuleEngineService.isRuleEngineAvailable()).thenReturn(true);
        when(droolsRuleEngineService.evaluateIpRisk(any(IpRiskFact.class))).thenReturn(riskFactWithResult);

        // When
        IpRiskAssessResponse response = ipRiskAssessmentService.assessIpRisk(ipAddress);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getIpAddress()).isEqualTo(ipAddress);
        assertThat(response.getRiskResult()).isEqualTo(RiskResult.REJECT);
        assertThat(response.getCountry()).isEqualTo("United States");
        assertThat(response.getCountryIsoCode()).isEqualTo("US");
        assertThat(response.getAssessmentDetails()).contains("outside mainland China");

        verify(ipGeolocationService).geolocateIp(ipAddress);
        verify(droolsRuleEngineService).evaluateIpRisk(any(IpRiskFact.class));
    }

    @Test
    @DisplayName("Should return error response when geolocation fails")
    void shouldReturnErrorResponseWhenGeolocationFails() {
        // Given
        String ipAddress = "invalid.ip";
        IpGeolocation invalidGeolocation = IpGeolocation.createInvalid(ipAddress, "Invalid IP address format");

        when(ipGeolocationService.isServiceAvailable()).thenReturn(true);
        when(ipGeolocationService.geolocateIp(ipAddress)).thenReturn(invalidGeolocation);

        // When
        IpRiskAssessResponse response = ipRiskAssessmentService.assessIpRisk(ipAddress);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getIpAddress()).isEqualTo(ipAddress);
        assertThat(response.getRiskResult()).isEqualTo(RiskResult.REJECT);
        assertThat(response.getCountry()).isEqualTo("Unknown");
        assertThat(response.getCountryIsoCode()).isEqualTo("Unknown");
        assertThat(response.getAssessmentDetails()).contains("Invalid IP address format");

        verify(ipGeolocationService).geolocateIp(ipAddress);
    }

    @Test
    @DisplayName("Should return error response when geolocation service is unavailable")
    void shouldReturnErrorResponseWhenGeolocationServiceUnavailable() {
        // Given
        String ipAddress = "************";

        when(ipGeolocationService.isServiceAvailable()).thenReturn(false);

        // When
        IpRiskAssessResponse response = ipRiskAssessmentService.assessIpRisk(ipAddress);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getIpAddress()).isEqualTo(ipAddress);
        assertThat(response.getRiskResult()).isEqualTo(RiskResult.REJECT);
        assertThat(response.getAssessmentDetails()).contains("Geolocation service is not available");
    }

    @Test
    @DisplayName("Should return error response when rule engine is unavailable")
    void shouldReturnErrorResponseWhenRuleEngineUnavailable() {
        // Given
        String ipAddress = "************";
        IpGeolocation geolocation = IpGeolocation.createValid(ipAddress, "China", "CN")
            .withSubdivision("Beijing")
            .withCity("Beijing")
            .withCoordinates(39.9042, 116.4074);

        when(ipGeolocationService.isServiceAvailable()).thenReturn(true);
        when(ipGeolocationService.geolocateIp(ipAddress)).thenReturn(geolocation);
        when(droolsRuleEngineService.isRuleEngineAvailable()).thenReturn(false);

        // When
        IpRiskAssessResponse response = ipRiskAssessmentService.assessIpRisk(ipAddress);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getIpAddress()).isEqualTo(ipAddress);
        assertThat(response.getRiskResult()).isEqualTo(RiskResult.REJECT);
        assertThat(response.getAssessmentDetails()).contains("Internal error during risk assessment");
    }

    @Test
    @DisplayName("Should check service availability correctly")
    void shouldCheckServiceAvailabilityCorrectly() {
        // Given
        when(ipGeolocationService.isServiceAvailable()).thenReturn(true);
        when(droolsRuleEngineService.isRuleEngineAvailable()).thenReturn(true);

        // When
        boolean isAvailable = ipRiskAssessmentService.isServiceAvailable();

        // Then
        assertThat(isAvailable).isTrue();

        // Test when geolocation service is unavailable
        when(ipGeolocationService.isServiceAvailable()).thenReturn(false);
        assertThat(ipRiskAssessmentService.isServiceAvailable()).isFalse();

        // Test when rule engine is unavailable
        when(ipGeolocationService.isServiceAvailable()).thenReturn(true);
        when(droolsRuleEngineService.isRuleEngineAvailable()).thenReturn(false);
        assertThat(ipRiskAssessmentService.isServiceAvailable()).isFalse();
    }
}
