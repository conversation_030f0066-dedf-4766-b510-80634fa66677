package com.kerryprops.kip.riskcontrol.service;

import com.kerryprops.kip.riskcontrol.model.IpGeolocation;
import com.kerryprops.kip.riskcontrol.model.IpRiskFact;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit tests for DroolsRuleEngineService.
 * Tests the Drools rules engine integration for IP risk assessment.
 */
@ExtendWith(MockitoExtension.class)
class DroolsRuleEngineServiceTest {

    @Mock
    private KieContainer kieContainer;

    @Mock
    private KieSession kieSession;

    private DroolsRuleEngineService droolsRuleEngineService;

    @BeforeEach
    void setUp() {
        droolsRuleEngineService = new DroolsRuleEngineService(kieContainer);
    }

    @Test
    @DisplayName("Should evaluate IP risk and return fact with results")
    void shouldEvaluateIpRiskAndReturnFactWithResults() {
        // Given
        String ipAddress = "************";
        IpGeolocation geolocation = IpGeolocation.createValid(ipAddress, "China", "CN")
            .withSubdivision("Beijing")
            .withCity("Beijing")
            .withCoordinates(39.9042, 116.4074);
        IpRiskFact riskFact = IpRiskFact.create(ipAddress, geolocation);

        when(kieContainer.newKieSession()).thenReturn(kieSession);
        when(kieSession.fireAllRules()).thenReturn(1);

        // When
        IpRiskFact result = droolsRuleEngineService.evaluateIpRisk(riskFact);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getIpAddress()).isEqualTo(ipAddress);
        assertThat(result.getGeolocation()).isEqualTo(geolocation);

        verify(kieContainer).newKieSession();
        verify(kieSession).insert(riskFact);
        verify(kieSession).fireAllRules();
        verify(kieSession).dispose();
    }

    @Test
    @DisplayName("Should dispose KieSession even when exception occurs")
    void shouldDisposeKieSessionEvenWhenExceptionOccurs() {
        // Given
        String ipAddress = "************";
        IpGeolocation geolocation = IpGeolocation.createValid(ipAddress, "China", "CN")
            .withSubdivision("Beijing")
            .withCity("Beijing")
            .withCoordinates(39.9042, 116.4074);
        IpRiskFact riskFact = IpRiskFact.create(ipAddress, geolocation);

        when(kieContainer.newKieSession()).thenReturn(kieSession);
        when(kieSession.fireAllRules()).thenThrow(new RuntimeException("Rule execution failed"));

        // When & Then
        assertThatThrownBy(() -> droolsRuleEngineService.evaluateIpRisk(riskFact))
            .isInstanceOf(RuntimeException.class)
            .hasMessage("Failed to evaluate IP risk rules");

        verify(kieSession).dispose();
    }

    @Test
    @DisplayName("Should check rule engine availability successfully")
    void shouldCheckRuleEngineAvailabilitySuccessfully() {
        // Given
        when(kieContainer.newKieSession()).thenReturn(kieSession);

        // When
        boolean isAvailable = droolsRuleEngineService.isRuleEngineAvailable();

        // Then
        assertThat(isAvailable).isTrue();
        verify(kieContainer).newKieSession();
        verify(kieSession).dispose();
    }

    @Test
    @DisplayName("Should return false when rule engine is not available")
    void shouldReturnFalseWhenRuleEngineNotAvailable() {
        // Given
        when(kieContainer.newKieSession()).thenThrow(new RuntimeException("KieContainer not available"));

        // When
        boolean isAvailable = droolsRuleEngineService.isRuleEngineAvailable();

        // Then
        assertThat(isAvailable).isFalse();
        verify(kieContainer).newKieSession();
    }

    @Test
    @DisplayName("Should handle KieSession creation failure")
    void shouldHandleKieSessionCreationFailure() {
        // Given
        String ipAddress = "************";
        IpGeolocation geolocation = IpGeolocation.createValid(ipAddress, "China", "CN")
            .withSubdivision("Beijing")
            .withCity("Beijing")
            .withCoordinates(39.9042, 116.4074);
        IpRiskFact riskFact = IpRiskFact.create(ipAddress, geolocation);

        when(kieContainer.newKieSession()).thenThrow(new RuntimeException("Failed to create KieSession"));

        // When & Then
        assertThatThrownBy(() -> droolsRuleEngineService.evaluateIpRisk(riskFact))
            .isInstanceOf(RuntimeException.class)
            .hasMessage("Failed to create KieSession");
    }

    @Test
    @DisplayName("Should handle fact insertion failure")
    void shouldHandleFactInsertionFailure() {
        // Given
        String ipAddress = "************";
        IpGeolocation geolocation = IpGeolocation.createValid(ipAddress, "China", "CN")
            .withSubdivision("Beijing")
            .withCity("Beijing")
            .withCoordinates(39.9042, 116.4074);
        IpRiskFact riskFact = IpRiskFact.create(ipAddress, geolocation);

        when(kieContainer.newKieSession()).thenReturn(kieSession);
        doThrow(new RuntimeException("Failed to insert fact")).when(kieSession).insert(any());

        // When & Then
        assertThatThrownBy(() -> droolsRuleEngineService.evaluateIpRisk(riskFact))
            .isInstanceOf(RuntimeException.class)
            .hasMessage("Failed to evaluate IP risk rules");

        verify(kieSession).dispose();
    }

    @Test
    @DisplayName("Should handle multiple rule executions")
    void shouldHandleMultipleRuleExecutions() {
        // Given
        String ipAddress = "************";
        IpGeolocation geolocation = IpGeolocation.createValid(ipAddress, "China", "CN")
            .withSubdivision("Beijing")
            .withCity("Beijing")
            .withCoordinates(39.9042, 116.4074);
        IpRiskFact riskFact = IpRiskFact.create(ipAddress, geolocation);

        when(kieContainer.newKieSession()).thenReturn(kieSession);
        when(kieSession.fireAllRules()).thenReturn(3); // Multiple rules executed

        // When
        IpRiskFact result = droolsRuleEngineService.evaluateIpRisk(riskFact);

        // Then
        assertThat(result).isNotNull();
        verify(kieSession).fireAllRules();
        verify(kieSession).dispose();
    }

    @Test
    @DisplayName("Should handle zero rule executions")
    void shouldHandleZeroRuleExecutions() {
        // Given
        String ipAddress = "************";
        IpGeolocation geolocation = IpGeolocation.createValid(ipAddress, "China", "CN")
            .withSubdivision("Beijing")
            .withCity("Beijing")
            .withCoordinates(39.9042, 116.4074);
        IpRiskFact riskFact = IpRiskFact.create(ipAddress, geolocation);

        when(kieContainer.newKieSession()).thenReturn(kieSession);
        when(kieSession.fireAllRules()).thenReturn(0); // No rules executed

        // When
        IpRiskFact result = droolsRuleEngineService.evaluateIpRisk(riskFact);

        // Then
        assertThat(result).isNotNull();
        verify(kieSession).fireAllRules();
        verify(kieSession).dispose();
    }
}
