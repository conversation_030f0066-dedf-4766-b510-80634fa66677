package com.kerryprops.kip.riskcontrol;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PhoneNumberUtilTest {

    @Mock
    private PhoneNumberProperties properties;
    
    @Mock
    private PhoneNumberProperties.VirtualPrefixes virtualPrefixes;

    @Test
    void isValidPhoneNumber_ShouldReturnTrue_WhenPhoneNumberIsValid() {
        var phoneNumberUtil = new PhoneNumberUtil(properties);
        assertThat(phoneNumberUtil.isValidPhoneNumber("13812345678")).isTrue();
        assertThat(phoneNumberUtil.isValidPhoneNumber("+8613812345678")).isTrue();
    }

    @Test
    void isValidPhoneNumber_ShouldReturnFalse_WhenPhoneNumberIsInvalid() {
        var phoneNumberUtil = new PhoneNumberUtil(properties);
        assertThat(phoneNumberUtil.isValidPhoneNumber("1381234567")).isTrue(); // 10位数字
        assertThat(phoneNumberUtil.isValidPhoneNumber("138123456789")).isTrue(); // 12位数字
        assertThat(phoneNumberUtil.isValidPhoneNumber("abcd12345678")).isFalse(); // 包含字母
        assertThat(phoneNumberUtil.isValidPhoneNumber("86-13812345678")).isFalse(); // 包含特殊字符
    }

    @Test
    void normalizePhoneNumber_ShouldRemovePlusEightySixPrefix() {
        var phoneNumberUtil = new PhoneNumberUtil(properties);
        assertThat(phoneNumberUtil.normalizePhoneNumber("+8613812345678")).isEqualTo("13812345678");
        assertThat(phoneNumberUtil.normalizePhoneNumber("13812345678")).isEqualTo("13812345678");
    }

    @Test
    void isVirtualNumber_ShouldReturnTrue_WhenPhoneNumberIsVirtual() {
        // 设置虚拟号段
        when(properties.getVirtualPrefixes()).thenReturn(virtualPrefixes);
        when(virtualPrefixes.getChinaMobile()).thenReturn(Arrays.asList("170", "171"));
        when(virtualPrefixes.getChinaUnicom()).thenReturn(Arrays.asList("165", "167"));
        when(virtualPrefixes.getChinaTelecom()).thenReturn(Arrays.asList("162", "1740"));
        when(virtualPrefixes.getChinaBroadcast()).thenReturn(Collections.singletonList("192"));
        
        var phoneNumberUtil = new PhoneNumberUtil(properties);
        assertThat(phoneNumberUtil.isVirtualNumber("17012345678")).isTrue(); // 中国移动虚拟号段
        assertThat(phoneNumberUtil.isVirtualNumber("17401234567")).isTrue(); // 中国电信4位虚拟号段
        assertThat(phoneNumberUtil.isVirtualNumber("+8617012345678")).isTrue(); // 带+86前缀
    }

    @Test
    void isVirtualNumber_ShouldReturnFalse_WhenPhoneNumberIsNotVirtual() {
        // 设置虚拟号段
        when(properties.getVirtualPrefixes()).thenReturn(virtualPrefixes);
        when(virtualPrefixes.getChinaMobile()).thenReturn(Arrays.asList("170", "171"));
        when(virtualPrefixes.getChinaUnicom()).thenReturn(Arrays.asList("165", "167"));
        when(virtualPrefixes.getChinaTelecom()).thenReturn(Arrays.asList("162", "1740"));
        when(virtualPrefixes.getChinaBroadcast()).thenReturn(Collections.singletonList("192"));
        
        var phoneNumberUtil = new PhoneNumberUtil(properties);
        assertThat(phoneNumberUtil.isVirtualNumber("13812345678")).isFalse(); // 普通号码
        assertThat(phoneNumberUtil.isVirtualNumber("123456")).isFalse(); // 长度不符
        assertThat(phoneNumberUtil.isVirtualNumber("19912345678")).isFalse(); // 非虚拟号段
    }
}