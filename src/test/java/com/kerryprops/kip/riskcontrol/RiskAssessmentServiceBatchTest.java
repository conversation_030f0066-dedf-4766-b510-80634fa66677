package com.kerryprops.kip.riskcontrol;

import com.kerryprops.kip.riskcontrol.constant.RiskLevel;
import com.kerryprops.kip.riskcontrol.constant.RiskType;
import com.kerryprops.kip.riskcontrol.vo.PhoneRiskAssessResponse;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Tests for the batch processing methods of RiskAssessmentService.
 */
@ExtendWith(MockitoExtension.class)
class RiskAssessmentServiceBatchTest {

    @Mock
    private PhoneNumberUtil phoneNumberUtil;

    @InjectMocks
    private RiskAssessmentService riskAssessmentService;

    /**
     * Test assessing a batch of mixed phone numbers.
     */
    @Test
    @DisplayName("Should assess a batch of mixed phone numbers")
    void shouldAssessBatchOfMixedPhoneNumbers() {
        // Arrange
        var regularNumber = "13912345678";
        var virtualNumber = "16612345678";
        var broadcastNumber = "19212345678";
        var invalidNumber = "123";
        var phoneNumbers = Arrays.asList(regularNumber, virtualNumber, broadcastNumber, invalidNumber);

        // Configure mocks for regular number
        when(phoneNumberUtil.isValidPhoneNumber(regularNumber)).thenReturn(true);
        when(phoneNumberUtil.normalizePhoneNumber(regularNumber)).thenReturn(regularNumber);
        when(phoneNumberUtil.isVirtualNumber(regularNumber)).thenReturn(false);

        // Configure mocks for virtual number
        when(phoneNumberUtil.isValidPhoneNumber(virtualNumber)).thenReturn(true);
        when(phoneNumberUtil.normalizePhoneNumber(virtualNumber)).thenReturn(virtualNumber);
        when(phoneNumberUtil.isVirtualNumber(virtualNumber)).thenReturn(true);

        // Configure mocks for broadcast number
        when(phoneNumberUtil.isValidPhoneNumber(broadcastNumber)).thenReturn(true);
        when(phoneNumberUtil.normalizePhoneNumber(broadcastNumber)).thenReturn(broadcastNumber);
        when(phoneNumberUtil.isVirtualNumber(broadcastNumber)).thenReturn(true);

        // Configure mocks for invalid number
        when(phoneNumberUtil.isValidPhoneNumber(invalidNumber)).thenReturn(false);

        // Act
        var results = riskAssessmentService.assessPhoneNumbers(phoneNumbers);

        // Assert
        assertNotNull(results);
        assertEquals(4, results.size(), "Should return results for all phone numbers");

        // Regular number assertions
        var regularResult = findResultByPhoneNumber(results, regularNumber);
        assertNotNull(regularResult, "Should have result for regular number");
        assertEquals(RiskLevel.LOW, regularResult.getRiskLevel(), "Regular number should have LOW risk level");
        assertTrue(regularResult.getRiskTypes()
                                .contains(RiskType.NONE), "Regular number should have NONE risk type");

        // Virtual number assertions
        var virtualResult = findResultByPhoneNumber(results, virtualNumber);
        assertNotNull(virtualResult, "Should have result for virtual number");
        assertEquals(RiskLevel.HIGH, virtualResult.getRiskLevel(), "Virtual number should have HIGH risk level");
        assertTrue(virtualResult.getRiskTypes()
                                .contains(RiskType.FRAUD), "Virtual number should have FRAUD risk type");

        // Broadcast number assertions
        var broadcastResult = findResultByPhoneNumber(results, broadcastNumber);
        assertNotNull(broadcastResult, "Should have result for broadcast number");
        assertEquals(RiskLevel.HIGH, broadcastResult.getRiskLevel(), "Broadcast number should have HIGH risk level");
        assertTrue(broadcastResult.getRiskTypes()
                                  .contains(RiskType.FRAUD), "Broadcast number should have FRAUD risk type");

        // Invalid number assertions
        var invalidResult = findResultByPhoneNumber(results, invalidNumber);
        assertNotNull(invalidResult, "Should have result for invalid number");
        assertEquals(RiskLevel.UNKNOWN, invalidResult.getRiskLevel(), "Invalid number should have UNKNOWN risk level");
        assertTrue(invalidResult.getRiskTypes()
                                .contains(RiskType.NONE), "Invalid number should have NONE risk type");
    }

    /**
     * Test assessing phone numbers with different formats (with and without +86 prefix).
     */
    @Test
    @DisplayName("Should handle phone numbers with different formats in batch processing")
    void shouldHandlePhoneNumbersWithDifferentFormatsInBatch() {
        // Arrange
        var phoneWithPrefix = "+8615012345678";
        var phoneWithoutPrefix = "15012345678";
        var phoneWithRegularPrefix = "8615012345678";
        var phoneNumbers = Arrays.asList(phoneWithPrefix, phoneWithoutPrefix, phoneWithRegularPrefix);

        when(phoneNumberUtil.isValidPhoneNumber(anyString())).thenReturn(true);
        when(phoneNumberUtil.normalizePhoneNumber(phoneWithPrefix)).thenReturn(phoneWithoutPrefix);
        when(phoneNumberUtil.normalizePhoneNumber(phoneWithoutPrefix)).thenReturn(phoneWithoutPrefix);
        when(phoneNumberUtil.normalizePhoneNumber(phoneWithRegularPrefix)).thenReturn(phoneWithoutPrefix);
        when(phoneNumberUtil.isVirtualNumber(phoneWithoutPrefix)).thenReturn(false);

        // Act
        var results = riskAssessmentService.assessPhoneNumbers(phoneNumbers);

        // Assert
        assertNotNull(results);
        assertEquals(3, results.size(), "Should return results for all phone numbers");

        // Each phone number should have same risk assessment since they normalize to the same number
        for (var result : results) {
            assertEquals(RiskLevel.LOW, result.getRiskLevel(), "All numbers should have LOW risk level");
            assertTrue(result.getRiskTypes()
                             .contains(RiskType.NONE), "All numbers should have NONE risk type");
        }

        // Original phone numbers should be preserved in the results
        var prefixResult = findResultByPhoneNumber(results, phoneWithPrefix);
        assertNotNull(prefixResult, "Should preserve original phone number with prefix");
        assertEquals(phoneWithPrefix, prefixResult.getPhoneNumber());

        var noPrefixResult = findResultByPhoneNumber(results, phoneWithoutPrefix);
        assertNotNull(noPrefixResult, "Should preserve original phone number without prefix");
        assertEquals(phoneWithoutPrefix, noPrefixResult.getPhoneNumber());

        var regularPrefixResult = findResultByPhoneNumber(results, phoneWithRegularPrefix);
        assertNotNull(regularPrefixResult, "Should preserve original phone number with regular prefix");
        assertEquals(phoneWithRegularPrefix, regularPrefixResult.getPhoneNumber());
    }

    /**
     * Test handling an empty phone number list.
     */
    @Test
    @DisplayName("Should return empty list for empty phone number list")
    void shouldReturnEmptyListForEmptyPhoneNumberList() {
        // Arrange
        var emptyList = Collections.<String>emptyList();

        // Act
        var results = riskAssessmentService.assessPhoneNumbers(emptyList);

        // Assert
        assertNotNull(results);
        assertTrue(results.isEmpty(), "Should return empty list for empty input");
    }

    /**
     * Test assessing phone numbers with specific virtual number prefixes.
     */
    @Test
    @DisplayName("Should correctly identify different carrier virtual numbers")
    void shouldCorrectlyIdentifyDifferentCarrierVirtualNumbers() {
        // Arrange - Test numbers for different carriers
        var chinaMobileVirtual = "1650123456";  // China Mobile virtual
        var chinaUnicomVirtual = "1655123456";  // China Unicom virtual
        var chinaTelecomVirtual = "1740123456"; // China Telecom virtual
        var chinaBroadcastVirtual = "1922123456"; // China Broadcast virtual

        var phoneNumbers =
                Arrays.asList(chinaMobileVirtual, chinaUnicomVirtual, chinaTelecomVirtual, chinaBroadcastVirtual);

        // Configure mocks
        for (var number : phoneNumbers) {
            when(phoneNumberUtil.isValidPhoneNumber(number)).thenReturn(true);
            when(phoneNumberUtil.normalizePhoneNumber(number)).thenReturn(number);
            when(phoneNumberUtil.isVirtualNumber(number)).thenReturn(true);
        }

        // Act
        var results = riskAssessmentService.assessPhoneNumbers(phoneNumbers);

        // Assert
        assertNotNull(results);
        assertEquals(4, results.size(), "Should return results for all virtual numbers");

        // All virtual numbers should have HIGH risk level and FRAUD risk type
        for (var result : results) {
            assertEquals(RiskLevel.HIGH, result.getRiskLevel(),
                         "Virtual number should have HIGH risk level: " + result.getPhoneNumber());
            assertTrue(result.getRiskTypes()
                             .contains(RiskType.FRAUD),
                       "Virtual number should have FRAUD risk type: " + result.getPhoneNumber());
        }
    }

    /**
     * Helper method to find a result by phone number.
     *
     * @param results     the list of results
     * @param phoneNumber the phone number to find
     * @return the matching result, or null if not found
     */
    private PhoneRiskAssessResponse findResultByPhoneNumber(List<PhoneRiskAssessResponse> results, String phoneNumber) {
        return results.stream()
                      .filter(result -> result.getPhoneNumber()
                                              .equals(phoneNumber))
                      .findFirst()
                      .orElse(null);
    }

}
