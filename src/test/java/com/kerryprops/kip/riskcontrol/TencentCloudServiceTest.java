package com.kerryprops.kip.riskcontrol;

import com.kerryprops.kip.exception.ApiAccessException;
import com.kerryprops.kip.riskcontrol.config.TencentCloudProperties;
import com.kerryprops.kip.riskcontrol.constant.RiskResult;
import com.kerryprops.kip.riskcontrol.constant.TxRiskAccountType;
import com.kerryprops.kip.riskcontrol.dto.PhoneTxRiskAssessRequest;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.rce.v20201103.RceClient;
import com.tencentcloudapi.rce.v20201103.models.ManageMarketingRiskResponse;
import com.tencentcloudapi.rce.v20201103.models.OutputManageMarketingRisk;
import com.tencentcloudapi.rce.v20201103.models.OutputManageMarketingRiskValue;
import org.instancio.Instancio;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.DigestUtils;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TencentCloudServiceTest {

    @Mock
    private RceClient rceClient;

    @Mock
    private TencentCloudProperties tencentCloudProperties;

    @InjectMocks
    private TencentCloudService tencentCloudService;

    @Test
    @DisplayName("assessPhoneRisk - valid inputs - returns PASS risk result")
    void assessPhoneRisk_validInput_returnsPass() throws TencentCloudSDKException {
        // Arrange
        String openId = "testOpenId";
        String phoneNumber = "1234567890";
        String ipAddress = "127.0.0.1";

        ManageMarketingRiskResponse mockResponse = new ManageMarketingRiskResponse();
        OutputManageMarketingRisk outputManageMarketingRisk = new OutputManageMarketingRisk();
        OutputManageMarketingRiskValue outputValue = new OutputManageMarketingRiskValue();
        outputValue.setRiskLevel("pass");
        outputManageMarketingRisk.setValue(outputValue);
        outputManageMarketingRisk.setCode(0L);
        mockResponse.setData(outputManageMarketingRisk);

        when(rceClient.ManageMarketingRisk(any())).thenReturn(mockResponse);
        PhoneTxRiskAssessRequest request = new PhoneTxRiskAssessRequest();
        request.setPhoneNumber(openId);
        request.setOpenId(phoneNumber);
        request.setIp(ipAddress);

        // Act
        var result = tencentCloudService.assessPhoneRisk(request);

        // Assert
        assertThat(result.getRiskResult()).isEqualTo(RiskResult.PASS);
    }

    @Test
    @DisplayName("assessPhoneRisk - TencentCloudSDKException thrown - throws exception")
    void assessPhoneRisk_serviceException_throwsException() throws TencentCloudSDKException {
        // Arrange
        String openId = "testOpenId";
        String phoneNumber = "1234567890";
        String ipAddress = "127.0.0.1";

        when(rceClient.ManageMarketingRisk(any())).thenThrow(new TencentCloudSDKException("error"));
        PhoneTxRiskAssessRequest request = new PhoneTxRiskAssessRequest();
        request.setPhoneNumber(openId);
        request.setOpenId(phoneNumber);
        request.setIp(ipAddress);

        // Act & Assert
        var abstractThrowableAssert = assertThatThrownBy(() -> tencentCloudService.assessPhoneRisk(request));
        abstractThrowableAssert.isInstanceOf(TencentCloudSDKException.class)
                               .hasMessageContaining("error");
    }

    @Test
    @DisplayName("assessPhoneRisk - null response data - throws ApiAccessException")
    void assessPhoneRisk_nullResponse_throwsApiAccessException() throws TencentCloudSDKException {
        // Arrange
        String openId = "testOpenId";
        String phoneNumber = "1234567890";
        String ipAddress = "127.0.0.1";

        ManageMarketingRiskResponse mockResponse = new ManageMarketingRiskResponse();
        when(rceClient.ManageMarketingRisk(any())).thenReturn(mockResponse);
        PhoneTxRiskAssessRequest request = new PhoneTxRiskAssessRequest();
        request.setPhoneNumber(openId);
        request.setOpenId(phoneNumber);
        request.setIp(ipAddress);
        // Act & Assert
        var abstractThrowableAssert = assertThatThrownBy(() -> tencentCloudService.assessPhoneRisk(request));
        abstractThrowableAssert.isInstanceOf(ApiAccessException.class)
                               .hasMessageContaining("ManageMarketingRisk result is empty");
    }

    @Test
    @DisplayName("assessPhoneRisk - risk level is REVIEW - returns REVIEW")
    void assessPhoneRisk_riskLevelReview_returnsReview() throws TencentCloudSDKException {
        // Arrange
        String openId = "testOpenId";
        String phoneNumber = "1234567890";
        String ipAddress = "127.0.0.1";

        ManageMarketingRiskResponse mockResponse = new ManageMarketingRiskResponse();
        OutputManageMarketingRisk outputManageMarketingRisk = new OutputManageMarketingRisk();
        OutputManageMarketingRiskValue outputValue = new OutputManageMarketingRiskValue();
        outputValue.setRiskLevel("review");
        outputManageMarketingRisk.setValue(outputValue);
        outputManageMarketingRisk.setCode(0L);
        mockResponse.setData(outputManageMarketingRisk);

        when(rceClient.ManageMarketingRisk(any())).thenReturn(mockResponse);
        PhoneTxRiskAssessRequest request = new PhoneTxRiskAssessRequest();
        request.setPhoneNumber(openId);
        request.setOpenId(phoneNumber);
        request.setIp(ipAddress);
        // Act
        var result = tencentCloudService.assessPhoneRisk(request);

        // Assert
        assertThat(result.getRiskResult()).isEqualTo(RiskResult.REVIEW);
    }

    @Test
    @DisplayName("assessPhoneRisk - risk level is REJECT - returns REJECT")
    void assessPhoneRisk_riskLevelReject_returnsReject() throws TencentCloudSDKException {
        // Arrange
        String openId = "testOpenId";
        String phoneNumber = "1234567890";
        String ipAddress = "127.0.0.1";

        ManageMarketingRiskResponse mockResponse = new ManageMarketingRiskResponse();
        OutputManageMarketingRisk outputManageMarketingRisk = new OutputManageMarketingRisk();
        OutputManageMarketingRiskValue outputValue = new OutputManageMarketingRiskValue();
        outputValue.setRiskLevel("reject");
        outputManageMarketingRisk.setValue(outputValue);
        outputManageMarketingRisk.setCode(0L);
        mockResponse.setData(outputManageMarketingRisk);

        when(rceClient.ManageMarketingRisk(any())).thenReturn(mockResponse);
        PhoneTxRiskAssessRequest request = new PhoneTxRiskAssessRequest();
        request.setPhoneNumber(openId);
        request.setOpenId(phoneNumber);
        request.setIp(ipAddress);
        // Act
        var result = tencentCloudService.assessPhoneRisk(request);

        // Assert
        assertThat(result.getRiskResult()).isEqualTo(RiskResult.REJECT);
    }

    @Test
    @DisplayName("buildAccountInfo - account type OPEN_ID - returns AccountInfo with WeChatAccountInfo")
    void buildAccountInfo_openId_returnsAccountInfo() {
        // Arrange
        String openId = Instancio.of(String.class)
                                 .create();
        String phoneNumber = Instancio.of(String.class)
                                      .create();

        // Act
        var accountInfo = tencentCloudService.buildAccountInfo(openId, phoneNumber);

        // Assert
        assertThat(accountInfo).isNotNull();
        assertThat(accountInfo.getAccountType()).isEqualTo(TxRiskAccountType.OPEN_ID.getTxAccountType());
        assertThat(accountInfo.getWeChatAccount()
                              .getWeChatOpenId()).isEqualTo(openId);
        assertThat(accountInfo.getWeChatAccount()
                              .getMobilePhone()).isEqualTo(DigestUtils.md5DigestAsHex(phoneNumber.getBytes()));
    }

    @Test
    @DisplayName("buildAccountInfo - account type PHONE_NUMBER - returns AccountInfo with OtherAccountInfo")
    void buildAccountInfo_phoneNumber_returnsAccountInfo() {
        // Arrange
        String phoneNumber = Instancio.of(String.class)
                                      .create();

        // Act
        var accountInfo = tencentCloudService.buildAccountInfo(null, phoneNumber);

        // Assert
        assertThat(accountInfo).isNotNull();
        assertThat(accountInfo.getAccountType()).isEqualTo(TxRiskAccountType.PHONE_NUMBER.getTxAccountType());
        assertThat(accountInfo.getOtherAccount()
                              .getAccountId()).isEqualTo(DigestUtils.md5DigestAsHex(phoneNumber.getBytes()));
    }

}