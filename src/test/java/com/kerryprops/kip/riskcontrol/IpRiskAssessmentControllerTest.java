package com.kerryprops.kip.riskcontrol;

import com.kerryprops.kip.exception.BadRequestException;
import com.kerryprops.kip.riskcontrol.constant.RiskResult;
import com.kerryprops.kip.riskcontrol.dto.IpRiskAssessRequest;
import com.kerryprops.kip.riskcontrol.dto.IpRiskAssessResponse;
import com.kerryprops.kip.riskcontrol.service.IpRiskAssessmentService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.validation.BindingResult;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit tests for IP risk assessment functionality in RiskAssessmentController.
 * Tests the new IP geolocation-based risk assessment endpoint.
 */
@ExtendWith(MockitoExtension.class)
class IpRiskAssessmentControllerTest {

    @Mock
    private RiskAssessmentService riskAssessmentService;

    @Mock
    private TencentCloudService tencentCloudService;

    @Mock
    private IpRiskAssessmentService ipRiskAssessmentService;

    @Mock
    private BindingResult bindingResult;

    @InjectMocks
    private RiskAssessmentController controller;

    @Test
    @DisplayName("Should return PASS for mainland China IP address")
    void shouldReturnPassForMainlandChinaIpAddress() {
        // Given
        String chinaIpAddress = "************";
        IpRiskAssessRequest request = new IpRiskAssessRequest(chinaIpAddress);
        
        IpRiskAssessResponse expectedResponse = new IpRiskAssessResponse(
            chinaIpAddress,
            RiskResult.PASS,
            "China",
            "CN",
            "IP address geolocated to mainland China",
            LocalDateTime.now()
        );

        when(bindingResult.hasErrors()).thenReturn(false);
        when(ipRiskAssessmentService.assessIpRisk(chinaIpAddress)).thenReturn(expectedResponse);

        // When
        IpRiskAssessResponse response = controller.assessIpAddress(request, bindingResult);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getIpAddress()).isEqualTo(chinaIpAddress);
        assertThat(response.getRiskResult()).isEqualTo(RiskResult.PASS);
        assertThat(response.getCountry()).isEqualTo("China");
        assertThat(response.getCountryIsoCode()).isEqualTo("CN");
        assertThat(response.getAssessmentDetails()).contains("mainland China");

        verify(ipRiskAssessmentService).assessIpRisk(chinaIpAddress);
    }

    @Test
    @DisplayName("Should return REJECT for non-mainland China IP address")
    void shouldReturnRejectForNonMainlandChinaIpAddress() {
        // Given
        String usIpAddress = "*******";
        IpRiskAssessRequest request = new IpRiskAssessRequest(usIpAddress);
        
        IpRiskAssessResponse expectedResponse = new IpRiskAssessResponse(
            usIpAddress,
            RiskResult.REJECT,
            "United States",
            "US",
            "IP address geolocated to United States, outside mainland China",
            LocalDateTime.now()
        );

        when(bindingResult.hasErrors()).thenReturn(false);
        when(ipRiskAssessmentService.assessIpRisk(usIpAddress)).thenReturn(expectedResponse);

        // When
        IpRiskAssessResponse response = controller.assessIpAddress(request, bindingResult);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getIpAddress()).isEqualTo(usIpAddress);
        assertThat(response.getRiskResult()).isEqualTo(RiskResult.REJECT);
        assertThat(response.getCountry()).isEqualTo("United States");
        assertThat(response.getCountryIsoCode()).isEqualTo("US");
        assertThat(response.getAssessmentDetails()).contains("outside mainland China");

        verify(ipRiskAssessmentService).assessIpRisk(usIpAddress);
    }

    @Test
    @DisplayName("Should throw BadRequestException when validation fails")
    void shouldThrowBadRequestExceptionWhenValidationFails() {
        // Given
        IpRiskAssessRequest request = new IpRiskAssessRequest("invalid.ip");

        when(bindingResult.hasErrors()).thenReturn(true);

        // When & Then
        assertThatThrownBy(() -> controller.assessIpAddress(request, bindingResult))
            .isInstanceOf(BadRequestException.class);

        verify(ipRiskAssessmentService, never()).assessIpRisk(anyString());
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "***********",      // Private IP
        "********",         // Private IP
        "**********",       // Private IP
        "127.0.0.1",        // Localhost
        "************",     // Public IP (China)
        "*******",          // Public IP (US)
        "*******"           // Public IP (Cloudflare)
    })
    @DisplayName("Should handle various valid IP address formats")
    void shouldHandleVariousValidIpAddressFormats(String ipAddress) {
        // Given
        IpRiskAssessRequest request = new IpRiskAssessRequest(ipAddress);
        
        IpRiskAssessResponse expectedResponse = new IpRiskAssessResponse(
            ipAddress,
            RiskResult.REJECT,
            "Unknown",
            "Unknown",
            "Test response",
            LocalDateTime.now()
        );

        when(bindingResult.hasErrors()).thenReturn(false);
        when(ipRiskAssessmentService.assessIpRisk(ipAddress)).thenReturn(expectedResponse);

        // When
        IpRiskAssessResponse response = controller.assessIpAddress(request, bindingResult);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getIpAddress()).isEqualTo(ipAddress);

        verify(ipRiskAssessmentService).assessIpRisk(ipAddress);
    }

    @Test
    @DisplayName("Should handle geolocation service errors gracefully")
    void shouldHandleGeolocationServiceErrorsGracefully() {
        // Given
        String ipAddress = "************";
        IpRiskAssessRequest request = new IpRiskAssessRequest(ipAddress);
        
        IpRiskAssessResponse errorResponse = IpRiskAssessResponse.createErrorResponse(
            ipAddress, "Geolocation service is not available"
        );

        when(bindingResult.hasErrors()).thenReturn(false);
        when(ipRiskAssessmentService.assessIpRisk(ipAddress)).thenReturn(errorResponse);

        // When
        IpRiskAssessResponse response = controller.assessIpAddress(request, bindingResult);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getIpAddress()).isEqualTo(ipAddress);
        assertThat(response.getRiskResult()).isEqualTo(RiskResult.REJECT);
        assertThat(response.getAssessmentDetails()).contains("Geolocation service is not available");

        verify(ipRiskAssessmentService).assessIpRisk(ipAddress);
    }

    @Test
    @DisplayName("Should handle IPv6 addresses")
    void shouldHandleIpv6Addresses() {
        // Given
        String ipv6Address = "2001:0db8:85a3:0000:0000:8a2e:0370:7334";
        IpRiskAssessRequest request = new IpRiskAssessRequest(ipv6Address);
        
        IpRiskAssessResponse expectedResponse = new IpRiskAssessResponse(
            ipv6Address,
            RiskResult.REJECT,
            "Unknown",
            "Unknown",
            "IPv6 address processed",
            LocalDateTime.now()
        );

        when(bindingResult.hasErrors()).thenReturn(false);
        when(ipRiskAssessmentService.assessIpRisk(ipv6Address)).thenReturn(expectedResponse);

        // When
        IpRiskAssessResponse response = controller.assessIpAddress(request, bindingResult);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getIpAddress()).isEqualTo(ipv6Address);

        verify(ipRiskAssessmentService).assessIpRisk(ipv6Address);
    }

    @Test
    @DisplayName("Should return error response for invalid IP format")
    void shouldReturnErrorResponseForInvalidIpFormat() {
        // Given
        String invalidIp = "999.999.999.999";
        IpRiskAssessRequest request = new IpRiskAssessRequest(invalidIp);
        
        IpRiskAssessResponse errorResponse = IpRiskAssessResponse.createErrorResponse(
            invalidIp, "Invalid IP address format"
        );

        when(bindingResult.hasErrors()).thenReturn(false);
        when(ipRiskAssessmentService.assessIpRisk(invalidIp)).thenReturn(errorResponse);

        // When
        IpRiskAssessResponse response = controller.assessIpAddress(request, bindingResult);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getIpAddress()).isEqualTo(invalidIp);
        assertThat(response.getRiskResult()).isEqualTo(RiskResult.REJECT);
        assertThat(response.getAssessmentDetails()).contains("Invalid IP address format");

        verify(ipRiskAssessmentService).assessIpRisk(invalidIp);
    }
}
