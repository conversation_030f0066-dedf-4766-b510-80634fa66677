package com.kerryprops.kip.riskcontrol.rules;

import com.kerryprops.kip.riskcontrol.model.IpRiskFact;
import com.kerryprops.kip.riskcontrol.constant.RiskResult;

/**
 * Drools rules for IP address risk assessment based on geographic location.
 * These rules evaluate IP addresses to determine if they originate from mainland China.
 * 
 * Assessment Logic:
 * - PASS: IP address is geolocated to mainland China (CN)
 * - REJECT: IP address is from any other geographic location or geolocation failed
 */

// Rule 1: PASS for mainland China IPs
rule "IP from mainland China - PASS"
    when
        $fact : IpRiskFact(
            geolocation != null,
            geolocation.valid == true,
            geolocation.countryIsoCode == "CN",
            riskResult == null
        )
    then
        $fact.setRiskResult(RiskResult.PASS);
        $fact.setAssessmentDetails("IP address geolocated to mainland China");
        System.out.println("Rule executed: IP from mainland China - PASS for " + $fact.getIpAddress());
end

// Rule 2: REJECT for non-mainland China IPs with valid geolocation
rule "IP from outside mainland China - REJECT"
    when
        $fact : IpRiskFact(
            geolocation != null,
            geolocation.valid == true,
            geolocation.countryIsoCode != "CN",
            riskResult == null
        )
    then
        $fact.setRiskResult(RiskResult.REJECT);
        $fact.setAssessmentDetails("IP address geolocated to " + $fact.getGeolocation().getCountry() + ", outside mainland China");
        System.out.println("Rule executed: IP from outside mainland China - REJECT for " + $fact.getIpAddress() + " (Country: " + $fact.getGeolocation().getCountry() + ")");
end

// Rule 3: REJECT for IPs with invalid geolocation
rule "IP with invalid geolocation - REJECT"
    when
        $fact : IpRiskFact(
            geolocation == null || geolocation.valid == false,
            riskResult == null
        )
    then
        $fact.setRiskResult(RiskResult.REJECT);
        $fact.setAssessmentDetails("IP geolocation failed or invalid");
        System.out.println("Rule executed: IP with invalid geolocation - REJECT for " + $fact.getIpAddress());
end

// Rule 4: Default fallback rule - REJECT
rule "Default IP assessment - REJECT"
    salience -100  // Lower priority, executes last
    when
        $fact : IpRiskFact(riskResult == null)
    then
        $fact.setRiskResult(RiskResult.REJECT);
        $fact.setAssessmentDetails("Default assessment: IP risk evaluation failed");
        System.out.println("Rule executed: Default IP assessment - REJECT for " + $fact.getIpAddress());
end
