package com.kerryprops.kip.riskcontrol.model;

import com.kerryprops.kip.riskcontrol.constant.RiskResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Drools fact object for IP risk assessment rules.
 * This object is inserted into the Drools working memory for rule evaluation.
 * 
 * <AUTHOR> Properties Limited
 * @since 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IpRiskFact {

    /**
     * The IP address being assessed.
     */
    private String ipAddress;

    /**
     * The geolocation information for the IP.
     */
    private IpGeolocation geolocation;

    /**
     * The risk assessment result determined by rules.
     * This field is set by Drools rules during evaluation.
     */
    private RiskResult riskResult;

    /**
     * Additional details about the risk assessment.
     * This field is set by Drools rules during evaluation.
     */
    private String assessmentDetails;

    /**
     * Creates a new IpRiskFact for rule evaluation.
     *
     * @param ipAddress the IP address to assess
     * @param geolocation the geolocation information
     * @return IpRiskFact ready for Drools evaluation
     */
    public static IpRiskFact create(String ipAddress, IpGeolocation geolocation) {
        return new IpRiskFact(ipAddress, geolocation, null, null);
    }

    /**
     * Checks if the IP is from mainland China based on geolocation.
     * This method can be used in Drools rule conditions.
     *
     * @return true if IP is from mainland China, false otherwise
     */
    public boolean isFromMainlandChina() {
        return geolocation != null && geolocation.isFromMainlandChina();
    }

    /**
     * Checks if the geolocation is valid.
     * This method can be used in Drools rule conditions.
     *
     * @return true if geolocation is valid, false otherwise
     */
    public boolean hasValidGeolocation() {
        return geolocation != null && geolocation.isValid();
    }

    /**
     * Gets the country ISO code from geolocation.
     * This method can be used in Drools rule conditions.
     *
     * @return the country ISO code, or null if not available
     */
    public String getCountryIsoCode() {
        return geolocation != null ? geolocation.getCountryIsoCode() : null;
    }

    /**
     * Gets the country name from geolocation.
     * This method can be used in Drools rule conditions.
     *
     * @return the country name, or null if not available
     */
    public String getCountryName() {
        return geolocation != null ? geolocation.getCountry() : null;
    }
}
