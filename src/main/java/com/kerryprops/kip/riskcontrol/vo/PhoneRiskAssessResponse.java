package com.kerryprops.kip.riskcontrol.vo;

import com.kerryprops.kip.riskcontrol.constant.RiskLevel;
import com.kerryprops.kip.riskcontrol.constant.RiskType;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Response model for phone risk assessment.
 * Contains risk information for a phone number.
 */
@Getter
@Setter
public class PhoneRiskAssessResponse {

    private String phoneNumber;

    private RiskLevel riskLevel;

    private List<RiskType> riskTypes;

    private LocalDateTime lastUpdated;

    /**
     * Constructs a response with all fields.
     *
     * @param phoneNumber the phone number
     * @param riskLevel   the risk level
     * @param riskTypes   the risk types
     * @param lastUpdated the last updated timestamp
     */
    public PhoneRiskAssessResponse(String phoneNumber, RiskLevel riskLevel, List<RiskType> riskTypes,
                                   LocalDateTime lastUpdated) {
        this.phoneNumber = phoneNumber;
        this.riskLevel = riskLevel;
        this.riskTypes = riskTypes;
        this.lastUpdated = lastUpdated;
    }

}
