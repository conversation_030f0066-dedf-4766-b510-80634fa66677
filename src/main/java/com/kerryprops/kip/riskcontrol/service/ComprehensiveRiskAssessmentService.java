package com.kerryprops.kip.riskcontrol.service;

import com.kerryprops.kip.riskcontrol.model.IpGeolocation;
import com.kerryprops.kip.riskcontrol.model.RiskAssessmentContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;
import org.springframework.stereotype.Service;

/**
 * 综合风控评估服务
 * 基于业务规则表实现的完整风控评估功能
 * 
 * <AUTHOR> Properties Limited
 * @since 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ComprehensiveRiskAssessmentService {

    private final KieContainer kieContainer;
    private final IpGeolocationService ipGeolocationService;

    /**
     * 执行综合风控评估
     * 
     * @param context 风控评估上下文
     * @return 评估后的上下文（包含结果）
     */
    public RiskAssessmentContext assessRisk(RiskAssessmentContext context) {
        log.info("开始综合风控评估 - 业务场景: {}, 商场: {}, IP: {}", 
                context.getBusinessScenario(), context.getMallCode(), context.getIpAddress());

        try {
            // 1. 如果有IP地址，进行地理位置查询
            if (context.getIpAddress() != null && context.getIpGeolocation() == null) {
                IpGeolocation geolocation = ipGeolocationService.geolocateIp(context.getIpAddress());
                context.setIpGeolocation(geolocation);
            }

            // 2. 执行Drools规则评估
            evaluateWithDroolsRules(context);

            log.info("综合风控评估完成 - 结果: {}, 详情: {}", 
                    context.getRiskResult(), context.getAssessmentDetails());

            return context;

        } catch (Exception e) {
            log.error("综合风控评估异常", e);
            context.setRiskResult(com.kerryprops.kip.riskcontrol.constant.RiskResult.REJECT);
            context.setAssessmentDetails("系统异常，评估失败");
            context.setBlockMessage("系统繁忙，请稍后重试");
            return context;
        }
    }

    /**
     * 使用Drools规则引擎进行评估
     * 
     * @param context 风控评估上下文
     */
    private void evaluateWithDroolsRules(RiskAssessmentContext context) {
        log.debug("开始Drools规则评估");

        KieSession kieSession = kieContainer.newKieSession();
        
        try {
            // 插入事实到工作内存
            kieSession.insert(context);
            
            // 执行所有适用的规则
            int rulesExecuted = kieSession.fireAllRules();
            
            log.info("Drools规则评估完成 - 执行规则数: {}, 最终结果: {}", 
                    rulesExecuted, context.getRiskResult());
            
        } catch (Exception e) {
            log.error("Drools规则评估异常", e);
            throw new RuntimeException("规则引擎评估失败", e);
        } finally {
            // 释放资源
            kieSession.dispose();
            log.debug("KieSession已释放");
        }
    }

    /**
     * 创建登录场景的风控评估上下文
     * 
     * @param mallCode 商场代码
     * @param ipAddress IP地址
     * @param phoneNumber 手机号
     * @param unionId UnionID
     * @param tencentRiskLevel 腾讯风控等级
     * @return 风控评估上下文
     */
    public RiskAssessmentContext createLoginContext(String mallCode, String ipAddress, String phoneNumber, 
                                                   String unionId, String tencentRiskLevel) {
        RiskAssessmentContext.MallCode mall = parseMallCode(mallCode);
        
        return RiskAssessmentContext.create(RiskAssessmentContext.BusinessScenario.LOGIN, mall)
                .withIpInfo(ipAddress, null)
                .withPhoneInfo(phoneNumber, isMainlandPhoneNumber(phoneNumber), isVirtualPhoneNumber(phoneNumber))
                .withRiskInfo(tencentRiskLevel, null, null);
    }

    /**
     * 创建会员停车权益场景的风控评估上下文
     * 
     * @param mallCode 商场代码
     * @param ipAddress IP地址
     * @param memberPoints 会员积分
     * @return 风控评估上下文
     */
    public RiskAssessmentContext createParkingBenefitContext(String mallCode, String ipAddress, Integer memberPoints) {
        RiskAssessmentContext.MallCode mall = parseMallCode(mallCode);
        
        return RiskAssessmentContext.create(RiskAssessmentContext.BusinessScenario.PARKING_BENEFIT, mall)
                .withIpInfo(ipAddress, null)
                .withMemberInfo(memberPoints, null);
    }

    /**
     * 创建拍照积分场景的风控评估上下文
     * 
     * @param mallCode 商场代码
     * @param ipAddress IP地址
     * @param phoneNumber 手机号
     * @param memberPoints 会员积分
     * @return 风控评估上下文
     */
    public RiskAssessmentContext createPhotoPointsContext(String mallCode, String ipAddress, String phoneNumber, Integer memberPoints) {
        RiskAssessmentContext.MallCode mall = parseMallCode(mallCode);
        
        return RiskAssessmentContext.create(RiskAssessmentContext.BusinessScenario.PHOTO_POINTS, mall)
                .withIpInfo(ipAddress, null)
                .withPhoneInfo(phoneNumber, isMainlandPhoneNumber(phoneNumber), isVirtualPhoneNumber(phoneNumber))
                .withMemberInfo(memberPoints, null);
    }

    /**
     * 创建营销场景的风控评估上下文
     * 
     * @param mallCode 商场代码
     * @param memberPoints 会员积分
     * @return 风控评估上下文
     */
    public RiskAssessmentContext createMarketingContext(String mallCode, Integer memberPoints) {
        RiskAssessmentContext.MallCode mall = parseMallCode(mallCode);
        
        return RiskAssessmentContext.create(RiskAssessmentContext.BusinessScenario.MARKETING, mall)
                .withMemberInfo(memberPoints, null);
    }

    /**
     * 解析商场代码
     * 
     * @param mallCode 商场代码字符串
     * @return MallCode枚举
     */
    private RiskAssessmentContext.MallCode parseMallCode(String mallCode) {
        if (mallCode == null) {
            return RiskAssessmentContext.MallCode.ALL;
        }
        
        try {
            return RiskAssessmentContext.MallCode.valueOf(mallCode.toUpperCase());
        } catch (IllegalArgumentException e) {
            log.warn("未知的商场代码: {}, 使用默认值ALL", mallCode);
            return RiskAssessmentContext.MallCode.ALL;
        }
    }

    /**
     * 判断是否为大陆手机号
     * 简化实现，实际应该使用更完整的号段判断逻辑
     * 
     * @param phoneNumber 手机号
     * @return 是否为大陆手机号
     */
    private Boolean isMainlandPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.length() < 11) {
            return false;
        }
        
        // 简化判断：以1开头的11位数字认为是大陆手机号
        return phoneNumber.matches("^1\\d{10}$");
    }

    /**
     * 判断是否为虚拟号段
     * 简化实现，实际应该维护完整的虚拟号段列表
     * 
     * @param phoneNumber 手机号
     * @return 是否为虚拟号段
     */
    private Boolean isVirtualPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.length() < 11) {
            return false;
        }
        
        // 简化判断：170、171号段认为是虚拟号段
        String prefix = phoneNumber.substring(0, 3);
        return "170".equals(prefix) || "171".equals(prefix);
    }

    /**
     * 检查服务是否可用
     * 
     * @return 服务是否可用
     */
    public boolean isServiceAvailable() {
        try {
            KieSession testSession = kieContainer.newKieSession();
            testSession.dispose();
            return true;
        } catch (Exception e) {
            log.warn("综合风控评估服务不可用", e);
            return false;
        }
    }
}
