package com.kerryprops.kip.riskcontrol.service;

import com.kerryprops.kip.riskcontrol.dto.IpRiskAssessResponse;
import com.kerryprops.kip.riskcontrol.model.IpGeolocation;
import com.kerryprops.kip.riskcontrol.model.IpRiskFact;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Service for IP-based risk assessment using Drools rules engine.
 * Integrates IP geolocation with Drools rules to determine risk levels.
 * 
 * <AUTHOR> Properties Limited
 * @since 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IpRiskAssessmentService {

    private final IpGeolocationService ipGeolocationService;
    private final DroolsRuleEngineService droolsRuleEngineService;

    /**
     * Assesses the risk level of an IP address based on geographic location.
     * This method follows functional programming principles by being stateless
     * and composing smaller functions.
     * 
     * @param ipAddress the IP address to assess
     * @return IpRiskAssessResponse containing the assessment result
     */
    public IpRiskAssessResponse assessIpRisk(String ipAddress) {
        log.info("Starting IP risk assessment for address: {}", ipAddress);

        try {
            // Step 1: Geolocate the IP address
            IpGeolocation geolocation = geolocateIpAddress(ipAddress);
            
            // Step 2: Handle geolocation failures
            if (!geolocation.isValid()) {
                log.warn("Geolocation failed for IP: {} - {}", ipAddress, geolocation.getErrorMessage());
                return IpRiskAssessResponse.createErrorResponse(ipAddress, geolocation.getErrorMessage());
            }
            
            // Step 3: Evaluate risk using Drools rules
            IpRiskFact riskFact = evaluateRiskWithRules(ipAddress, geolocation);
            
            // Step 4: Convert to response DTO
            IpRiskAssessResponse response = convertToResponse(riskFact);
            
            log.info("IP risk assessment completed - IP: {}, Result: {}, Country: {}", 
                    ipAddress, response.getRiskResult(), response.getCountry());
            
            return response;
            
        } catch (RuntimeException e) {
            log.error("Unexpected error during IP risk assessment for: {}", ipAddress, e);
            return IpRiskAssessResponse.createErrorResponse(ipAddress,
                    "Internal error during risk assessment: " + e.getMessage());
        }
    }

    /**
     * Performs IP geolocation lookup.
     * 
     * @param ipAddress the IP address to geolocate
     * @return IpGeolocation containing geographic information
     */
    private IpGeolocation geolocateIpAddress(String ipAddress) {
        log.debug("Performing geolocation lookup for IP: {}", ipAddress);
        
        if (!ipGeolocationService.isServiceAvailable()) {
            log.warn("IP geolocation service is not available");
            return IpGeolocation.createInvalid(ipAddress, "Geolocation service is not available");
        }
        
        return ipGeolocationService.geolocateIp(ipAddress);
    }

    /**
     * Evaluates IP risk using Drools rules engine.
     * 
     * @param ipAddress the IP address being assessed
     * @param geolocation the geolocation information
     * @return IpRiskFact with risk assessment results
     */
    private IpRiskFact evaluateRiskWithRules(String ipAddress, IpGeolocation geolocation) {
        log.debug("Evaluating IP risk with Drools rules for: {}", ipAddress);
        
        if (!droolsRuleEngineService.isRuleEngineAvailable()) {
            log.warn("Drools rule engine is not available");
            throw new RuntimeException("Rule engine is not available");
        }
        
        IpRiskFact riskFact = IpRiskFact.create(ipAddress, geolocation);
        return droolsRuleEngineService.evaluateIpRisk(riskFact);
    }

    /**
     * Converts IpRiskFact to IpRiskAssessResponse.
     * 
     * @param riskFact the fact object with assessment results
     * @return IpRiskAssessResponse DTO
     */
    private IpRiskAssessResponse convertToResponse(IpRiskFact riskFact) {
        IpGeolocation geolocation = riskFact.getGeolocation();
        
        return new IpRiskAssessResponse(
            riskFact.getIpAddress(),
            riskFact.getRiskResult(),
            geolocation.getCountry(),
            geolocation.getCountryIsoCode(),
            riskFact.getAssessmentDetails(),
            java.time.LocalDateTime.now()
        );
    }

    /**
     * Checks if the IP risk assessment service is fully operational.
     * 
     * @return true if all required services are available, false otherwise
     */
    public boolean isServiceAvailable() {
        return ipGeolocationService.isServiceAvailable() && 
               droolsRuleEngineService.isRuleEngineAvailable();
    }
}
