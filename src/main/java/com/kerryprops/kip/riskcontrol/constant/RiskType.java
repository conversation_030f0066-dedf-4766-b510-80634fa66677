package com.kerryprops.kip.riskcontrol.constant;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * Enumeration of risk types for phone number assessment.
 */
public enum RiskType {

    /**
     * Fraud risk type.
     */
    FRAUD("fraud"),

    /**
     * No risk type.
     */
    NONE("none");

    private final String value;

    /**
     * Constructor for risk type enum.
     *
     * @param value string representation of risk type
     */
    RiskType(String value) {
        this.value = value;
    }

    /**
     * Gets the string value of the risk type.
     *
     * @return string representation of risk type
     */
    @JsonValue
    public String getValue() {
        return value;
    }
}
