package com.kerryprops.kip.riskcontrol.constant;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * TxRiskAccountType.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Yu 2025-05-21 11:14:23
 **/
@RequiredArgsConstructor
@Getter
public enum TxRiskAccountType {
    /**
     * 微信openId.
     */
    OPEN_ID(2L),
    /**
     * 手机号.
     */
    PHONE_NUMBER(10004L);

    private final long txAccountType;

    /**
     * 根据传入的openId返回对应的账户类型.
     * 如果openId为空或仅包含空白字符，返回手机号类型账户；否则返回微信openId类型账户.
     *
     * @param openId 用于判断账户类型的openId.
     * @return 对应的TxRiskAccountType枚举值.
     */
    public static TxRiskAccountType of(String openId) {
        if (StringUtils.isBlank(openId)) {
            return PHONE_NUMBER;
        }
        return OPEN_ID;
    }
}
