package com.kerryprops.kip.riskcontrol.constant;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * Enumeration of risk levels for phone number assessment.
 */
public enum RiskLevel {

    /**
     * Low risk level.
     */
    LOW("low"),

    /**
     * High risk level.
     */
    HIGH("high"),

    /**
     * Unknown risk level.
     */
    UNKNOWN("unknown");

    private final String value;

    /**
     * Constructor for risk level enum.
     *
     * @param value string representation of risk level
     */
    RiskLevel(String value) {
        this.value = value;
    }

    /**
     * Gets the string value of the risk level.
     *
     * @return string representation of risk level
     */
    @JsonValue
    public String getValue() {
        return value;
    }
}
