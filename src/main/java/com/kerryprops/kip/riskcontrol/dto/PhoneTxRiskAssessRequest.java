package com.kerryprops.kip.riskcontrol.dto;

import com.kerryprops.kip.riskcontrol.constant.TxRiskSceneCode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

/**
 * PhoneTxRiskAssessRequest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Yu 2025-05-20 11:15:56
 **/
@Getter
@Setter
public class PhoneTxRiskAssessRequest {

    @Schema(title = "手机号")
    private String phoneNumber;

    @Schema(title = "微信号id")
    private String openId;

    @NotBlank
    @Schema(title = "请求ip", description = "传入用户非外网ip会影响判断结果")
    private String ip;

    @Schema(title = "场景", description = "默认值：LOGIN")
    private TxRiskSceneCode sceneCode = TxRiskSceneCode.LOGIN;

}
