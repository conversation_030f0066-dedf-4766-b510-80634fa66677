package com.kerryprops.kip.riskcontrol.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Request DTO for IP address risk assessment.
 * Contains the IP address to be evaluated for geographic location-based risk.
 * 
 * <AUTHOR> Properties Limited
 * @since 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "IP地址风险评估请求")
public class IpRiskAssessRequest {

    /**
     * The IP address to assess for risk.
     * Must be a valid IPv4 or IPv6 address format.
     */
    @NotBlank(message = "IP address cannot be blank")
    @Pattern(
        regexp = "^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$" +
                "|^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$",
        message = "Invalid IP address format"
    )
    @Schema(
        description = "待评估的IP地址",
        example = "************",
        required = true
    )
    private String ipAddress;
}
