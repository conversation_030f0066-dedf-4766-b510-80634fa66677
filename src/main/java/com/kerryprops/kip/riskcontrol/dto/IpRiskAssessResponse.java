package com.kerryprops.kip.riskcontrol.dto;

import com.kerryprops.kip.riskcontrol.constant.RiskResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Response DTO for IP address risk assessment.
 * Contains the assessment result and geographic information.
 * 
 * <AUTHOR> Properties Limited
 * @since 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "IP地址风险评估响应")
public class IpRiskAssessResponse {

    /**
     * The original IP address that was assessed.
     */
    @Schema(description = "被评估的IP地址", example = "************")
    private String ipAddress;

    /**
     * The risk assessment result.
     * PASS if IP is from mainland China, REJECT otherwise.
     */
    @Schema(description = "风险评估结果", example = "PASS")
    private RiskResult riskResult;

    /**
     * The detected country of the IP address.
     */
    @Schema(description = "IP地址所在国家", example = "China")
    private String country;

    /**
     * The detected country ISO code of the IP address.
     */
    @Schema(description = "IP地址所在国家ISO代码", example = "CN")
    private String countryIsoCode;

    /**
     * Additional details about the assessment.
     */
    @Schema(description = "评估详情", example = "IP address geolocated to mainland China")
    private String assessmentDetails;

    /**
     * Timestamp when the assessment was performed.
     */
    @Schema(description = "评估时间")
    private LocalDateTime assessmentTime;

    /**
     * Creates a PASS response for mainland China IPs.
     *
     * @param ipAddress the assessed IP address
     * @param country the detected country
     * @param countryIsoCode the country ISO code
     * @return IpRiskAssessResponse with PASS result
     */
    public static IpRiskAssessResponse createPassResponse(String ipAddress, String country, String countryIsoCode) {
        return new IpRiskAssessResponse(
            ipAddress,
            RiskResult.PASS,
            country,
            countryIsoCode,
            "IP address geolocated to mainland China",
            LocalDateTime.now()
        );
    }

    /**
     * Creates a REJECT response for non-mainland China IPs.
     *
     * @param ipAddress the assessed IP address
     * @param country the detected country
     * @param countryIsoCode the country ISO code
     * @return IpRiskAssessResponse with REJECT result
     */
    public static IpRiskAssessResponse createRejectResponse(String ipAddress, String country, String countryIsoCode) {
        return new IpRiskAssessResponse(
            ipAddress,
            RiskResult.REJECT,
            country,
            countryIsoCode,
            String.format("IP address geolocated to %s, outside mainland China", country),
            LocalDateTime.now()
        );
    }

    /**
     * Creates an error response when geolocation fails.
     *
     * @param ipAddress the assessed IP address
     * @param errorMessage the error details
     * @return IpRiskAssessResponse with REJECT result and error details
     */
    public static IpRiskAssessResponse createErrorResponse(String ipAddress, String errorMessage) {
        return new IpRiskAssessResponse(
            ipAddress,
            RiskResult.REJECT,
            "Unknown",
            "Unknown",
            String.format("Geolocation failed: %s", errorMessage),
            LocalDateTime.now()
        );
    }
}
