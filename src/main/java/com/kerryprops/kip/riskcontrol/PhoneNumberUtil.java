package com.kerryprops.kip.riskcontrol;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * Utility class for phone number operations.
 */
@Component
@RequiredArgsConstructor
public class PhoneNumberUtil {
    /**
     * Regular expression pattern for valid phone numbers.
     * Allows phone numbers with optional +86 or 86 prefix.
     * The number must start with 1 after any prefix.
     */
    public static final Pattern PHONE_PATTERN = Pattern.compile("^(\\+?86)?1\\d+$");

    private final PhoneNumberProperties properties;

    private Set<String> getAllVirtualPrefixes() {
        Set<String> allPrefixes = new HashSet<>();
        allPrefixes.addAll(properties.getVirtualPrefixes()
                                     .getChinaMobile());
        allPrefixes.addAll(properties.getVirtualPrefixes()
                                     .getChinaUnicom());
        allPrefixes.addAll(properties.getVirtualPrefixes()
                                     .getChinaTelecom());
        allPrefixes.addAll(properties.getVirtualPrefixes()
                                     .getChinaBroadcast());
        return allPrefixes;
    }

    /**
     * Validates if a phone number has valid format.
     *
     * @param phoneNumber the phone number to validate
     * @return true if the phone number is valid, false otherwise
     */
    public boolean isValidPhoneNumber(String phoneNumber) {
        return PHONE_PATTERN.matcher(phoneNumber)
                            .matches();
    }

    /**
     * Normalizes a phone number by removing the +86 or 86 prefix if present.
     *
     * @param phoneNumber the phone number to normalize
     * @return the normalized phone number
     */
    public String normalizePhoneNumber(String phoneNumber) {
        if (phoneNumber.startsWith("+86")) {
            return phoneNumber.substring(3);
        } else if (phoneNumber.startsWith("86") && phoneNumber.length() > 2 && phoneNumber.charAt(2) == '1') {
            return phoneNumber.substring(2);
        }
        return phoneNumber;
    }

    /**
     * Checks if a phone number belongs to a virtual number segment.
     *
     * @param phoneNumber the phone number to check
     * @return true if the phone number is a virtual number, false otherwise
     */
    public boolean isVirtualNumber(String phoneNumber) {
        var normalizedNumber = normalizePhoneNumber(phoneNumber);

        if (normalizedNumber.length() != 11) {
            return false;
        }

        var prefix3 = normalizedNumber.substring(0, 3);
        var prefix4 = normalizedNumber.substring(0, 4);
        var allPrefixes = getAllVirtualPrefixes();

        return allPrefixes.contains(prefix3) || allPrefixes.contains(prefix4);
    }

}
