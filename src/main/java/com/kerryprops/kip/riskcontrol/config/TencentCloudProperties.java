package com.kerryprops.kip.riskcontrol.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "tencent.cloud")
public class TencentCloudProperties {

    /**
     * 腾讯云风控引擎配置.
     */
    private RceProperties rce;

    private String dataProviderName = "嘉里（中国）项目管理有限公司上海分公司";

    private String dataRecipientName = "腾讯云计算（北京）有限责任公司";

    @Getter
    @Setter
    public static class RceProperties {

        private String secretId;

        private String secretKey;

        private String region;

    }

}