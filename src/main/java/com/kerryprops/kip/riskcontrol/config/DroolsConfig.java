package com.kerryprops.kip.riskcontrol.config;

import lombok.extern.slf4j.Slf4j;
import org.kie.api.KieServices;
import org.kie.api.builder.KieBuilder;
import org.kie.api.builder.KieFileSystem;
import org.kie.api.builder.KieRepository;
import org.kie.api.builder.ReleaseId;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;
import org.kie.internal.io.ResourceFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;

/**
 * Configuration class for Drools rules engine.
 * Sets up the KieContainer and KieSession for IP risk assessment rules.
 * 
 * <AUTHOR> Properties Limited
 * @since 1.0
 */
@Slf4j
@Configuration
public class DroolsConfig {

    private static final String RULES_PATH = "rules/ip-risk-assessment.drl";

    /**
     * Creates and configures the KieContainer for IP risk assessment rules.
     * 
     * @return configured <PERSON>eContainer
     * @throws IOException if rule files cannot be loaded
     */
    @Bean
    public KieContainer kieContainer() throws IOException {
        log.info("Initializing Drools KieContainer for IP risk assessment");

        KieServices kieServices = KieServices.Factory.get();
        KieRepository kieRepository = kieServices.getRepository();
        
        // Create a unique release ID for this rule set
        ReleaseId releaseId = kieServices.newReleaseId("com.kerryprops.kip", "ip-risk-rules", "1.0.0");
        
        // Create KieFileSystem and add rule files
        KieFileSystem kieFileSystem = kieServices.newKieFileSystem();
        
        // Load the IP risk assessment rules
        ClassPathResource rulesResource = new ClassPathResource(RULES_PATH);
        if (!rulesResource.exists()) {
            throw new IOException("Drools rule file not found: " + RULES_PATH);
        }
        
        kieFileSystem.write(ResourceFactory.newClassPathResource(RULES_PATH));
        kieFileSystem.generateAndWritePomXML(releaseId);
        
        // Build the rules
        KieBuilder kieBuilder = kieServices.newKieBuilder(kieFileSystem);
        kieBuilder.buildAll();
        
        // Check for compilation errors
        if (kieBuilder.getResults().hasMessages(org.kie.api.builder.Message.Level.ERROR)) {
            log.error("Drools rule compilation errors: {}", kieBuilder.getResults().getMessages());
            throw new RuntimeException("Failed to compile Drools rules: " + kieBuilder.getResults().getMessages());
        }
        
        // Install the rules in the repository
        kieRepository.addKieModule(kieBuilder.getKieModule());
        
        // Create and return the KieContainer
        KieContainer kieContainer = kieServices.newKieContainer(releaseId);
        log.info("Drools KieContainer initialized successfully with rules from: {}", RULES_PATH);
        
        return kieContainer;
    }

    /**
     * Creates a prototype KieSession bean.
     * Each request for this bean will return a new KieSession instance.
     * 
     * @param kieContainer the configured KieContainer
     * @return new KieSession instance
     */
    @Bean
    public KieSession kieSession(KieContainer kieContainer) {
        KieSession kieSession = kieContainer.newKieSession();
        log.debug("Created new KieSession for IP risk assessment");
        return kieSession;
    }
}
