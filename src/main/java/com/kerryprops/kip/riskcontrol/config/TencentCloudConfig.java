package com.kerryprops.kip.riskcontrol.config;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.common.profile.Language;
import com.tencentcloudapi.rce.v20201103.RceClient;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

@Configuration
@RequiredArgsConstructor
public class TencentCloudConfig {

    private final TencentCloudProperties tencentCloudProperties;

    @Bean
    public RceClient rceClient() {
        var rceSettings = tencentCloudProperties.getRce();
        String secretId = rceSettings.getSecretId();
        String secretKey = rceSettings.getSecretKey();
        Credential credential = new Credential(secretId, secretKey);

        // 实例化一个http选项，可选的，没有特殊需求可以跳过
        HttpProfile httpProfile = new HttpProfile();
        // 请求连接超时时间，单位为秒(默认60秒)
        httpProfile.setConnTimeout((int) Duration.ofSeconds(10)
                                                 .toSeconds());
        // 设置写入超时时间，单位为秒(默认0秒)
        httpProfile.setWriteTimeout((int) Duration.ofMinutes(5)
                                                  .toSeconds());
        // 设置读取超时时间，单位为秒(默认0秒)
        httpProfile.setReadTimeout((int) Duration.ofMinutes(5)
                                                 .toSeconds());

        // 实例化一个client选项，可选的，没有特殊需求可以跳过
        ClientProfile clientProfile = new ClientProfile();
        // 指定签名算法(默认为HmacSHA256)
        clientProfile.setSignMethod(ClientProfile.SIGN_SHA256);
        // 自3.1.80版本开始，SDK 支持打印日志。
        clientProfile.setHttpProfile(httpProfile);
        // clientProfile.setDebug(true);
        // 从3.1.16版本开始，支持设置公共参数 Language, 默认不传，选择(ZH_CN or EN_US)
        clientProfile.setLanguage(Language.ZH_CN);
        // 实例化要请求产品(以rce为例)的client对象,clientProfile是可选的
        return new RceClient(credential, rceSettings.getRegion(), clientProfile);
    }

}
